<x-filament-widgets::widget>
    <x-filament::section>
        <div class="filament-apex-charts-heading text-base font-semibold leading-6">
            <PERSON>ust<PERSON> e receitas por orçamento
        </div>
        <div class="space-y-6">
            <form wire:change.lazy="fetchData" class="space-y-6">
                <div class="grid grid-cols-1 gap-4">
                    {{ $this->form }}
                </div>
            </form>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Incomes Table -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <span class="text-emerald-500 mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            Receitas
                        </h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th class="px-4 py-3">Cliente</th>
                                    <th class="px-4 py-3">Descrição</th>
                                    <th class="px-4 py-3">Data</th>
                                    <th class="px-4 py-3 text-right">Valor</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($incomes as $income)
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-4 py-3">{{ $income->customer_name }}</td>
                                        <td class="px-4 py-3">{{ $income->description }}</td>
                                        <td class="px-4 py-3">{{ \Carbon\Carbon::parse($income->paid_at)->format('d/m/Y') }}</td>
                                        <td class="px-4 py-3 text-right font-medium text-emerald-600 dark:text-emerald-500">
                                            R$ {{ number_format($income->amount, 2, ',', '.') }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="4" class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                                            Nenhuma receita encontrada
                                        </td>
                                    </tr>
                                @endforelse
                                @if(count($incomes) > 0)
                                    <tr class="bg-gray-50 dark:bg-gray-700">
                                        <td colspan="3" class="px-4 py-3 text-right font-medium">Total:</td>
                                        <td class="px-4 py-3 text-right font-bold text-emerald-600 dark:text-emerald-500">
                                            R$ {{ number_format($incomes->sum('amount'), 2, ',', '.') }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Expenses Table -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                            <span class="text-red-500 mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd" />
                                </svg>
                            </span>
                            Despesas
                        </h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th class="px-4 py-3">Cliente</th>
                                    <th class="px-4 py-3">Descrição</th>
                                    <th class="px-4 py-3">Data</th>
                                    <th class="px-4 py-3 text-right">Valor</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($expenses as $expense)
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-4 py-3">{{ $expense->customer_name }}</td>
                                        <td class="px-4 py-3">{{ $expense->description }}</td>
                                        <td class="px-4 py-3">{{ \Carbon\Carbon::parse($expense->paid_at)->format('d/m/Y') }}</td>
                                        <td class="px-4 py-3 text-right font-medium text-red-600 dark:text-red-500">
                                            R$ {{ number_format($expense->amount, 2, ',', '.') }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="4" class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                                            Nenhuma despesa encontrada
                                        </td>
                                    </tr>
                                @endforelse
                                @if(count($expenses) > 0)
                                    <tr class="bg-gray-50 dark:bg-gray-700">
                                        <td colspan="3" class="px-4 py-3 text-right font-medium">Total:</td>
                                        <td class="px-4 py-3 text-right font-bold text-red-600 dark:text-red-500">
                                            R$ {{ number_format($expenses->sum('amount'), 2, ',', '.') }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Balance Summary -->
            @if(count($incomes) > 0 || count($expenses) > 0)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Balanço</h3>
                        @php
                            $totalIncomes = $incomes->sum('amount');
                            $totalExpenses = $expenses->sum('amount');
                            $balance = $totalIncomes - $totalExpenses;
                            $isPositive = $balance >= 0;
                        @endphp
                        <div class="text-right">
                            <p class="text-sm text-gray-500 dark:text-gray-400">Total Receitas - Total Despesas</p>
                            <p class="text-xl font-bold {{ $isPositive ? 'text-emerald-600 dark:text-emerald-500' : 'text-red-600 dark:text-red-500' }}">
                                R$ {{ number_format($balance, 2, ',', '.') }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
