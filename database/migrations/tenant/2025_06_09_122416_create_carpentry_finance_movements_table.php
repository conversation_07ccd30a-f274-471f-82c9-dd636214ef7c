<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carpentry_finance_movements', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('chart_of_accounts')->nullable();
            $table->string('customer_name');
            $table->string('description')->nullable();
            $table->string('quote_number')->nullable();
            $table->date('referring_date');
            $table->date('expires_at');
            $table->date('paid_at')->nullable();
            $table->string('payment_type');
            $table->decimal('amount', 14, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carpentry_finance_movements');
    }
};
