<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CarpentryFinanceMovementResource\Pages;
use App\Models\CarpentryFinanceMovement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CarpentryFinanceMovementResource extends Resource
{
    protected static ?string $model = CarpentryFinanceMovement::class;
    protected static ?string $modelLabel = 'Movimento financeiro';
    protected static ?string $pluralModelLabel = 'Movimentos financeiros';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('type')
                ->label('Tipo'),
            Forms\Components\TextInput::make('chart_of_accounts')
                ->label('Plano de contas'),
            Forms\Components\TextInput::make('customer_name')
                ->label('Cliente'),
            Forms\Components\TextInput::make('description')
                ->label('Descrição'),
            Forms\Components\TextInput::make('quote_number')
                ->label('Orçamento'),
            Forms\Components\DatePicker::make('referring_date')
                ->label('Competência')
                ->displayFormat('d/m/Y'),
            Forms\Components\DatePicker::make('expires_at')
                ->label('Data de vencimento')
                ->displayFormat('d/m/Y'),
            Forms\Components\DatePicker::make('paid_at')
                ->label('Data de pagamento')
                ->displayFormat('d/m/Y'),
            Forms\Components\TextInput::make('payment_type')
                ->label('Forma de pagamento'),
            Forms\Components\TextInput::make('amount')
                ->label('Valor')
                ->formatStateUsing(fn ($state) => 'R$ ' . number_format($state, 2, ',', '.')),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label('Tipo'),
                Tables\Columns\TextColumn::make('chart_of_accounts')
                    ->label('Plano de contas'),
                Tables\Columns\TextColumn::make('customer_name')
                    ->label('Cliente'),
                Tables\Columns\TextColumn::make('description')
                    ->label('Descrição'),
                Tables\Columns\TextColumn::make('quote_number')
                    ->label('Orçamento'),
                Tables\Columns\TextColumn::make('referring_date')
                    ->label('Competência')
                    ->date('d/m/Y'),
                Tables\Columns\TextColumn::make('expires_at')
                    ->label('Data de vencimento')
                    ->date('d/m/Y'),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label('Data de pagamento')
                    ->date('d/m/Y'),
                Tables\Columns\TextColumn::make('payment_type')
                    ->label('Forma de pagamento'),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Valor')
                    ->money('BRL'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCarpentryFinanceMovements::route('/'),
        ];
    }
}
