<?php

namespace App\Filament\Resources\CarpentryFinanceMovementResource\Pages;

use App\Filament\Resources\CarpentryFinanceMovementResource;
use App\Imports\CarpentryFinanceMovementImport;
use Filament\Resources\Pages\ManageRecords;

class ManageCarpentryFinanceMovements extends ManageRecords
{
    protected static string $resource = CarpentryFinanceMovementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            \EightyNine\ExcelImport\ExcelImportAction::make('import')
                ->label('Importar')
                ->icon('heroicon-o-document-arrow-up')
                ->modalHeading('Importar via template')
                ->modalDescription('Aqui, você pode importar suas planilhas.')
                ->uploadField(fn($upload) => $upload->label('Arquivo'))
                ->use(CarpentryFinanceMovementImport::class),
        ];
    }
}
