<?php

namespace App\Filament\Widgets;

use App\Models\CarpentryFinanceMovement;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CashFlow<PERSON>hart extends ApexChartWidget
{
    protected int | string | array $columnSpan = 12;
    protected static ?int $sort = 1;
    protected static ?string $chartId = 'cashFlowChart';
    protected static ?string $heading = 'Fluxo de caixa';

    protected function getOptions(): array
    {
        $start = Carbon::parse($this->filterFormData['Pagamento em de']);
        $end = Carbon::parse($this->filterFormData['Pagamento em até']);

        $data = CarpentryFinanceMovement::query()
            ->select([
                DB::raw('date_format(paid_at, \'%d/%m/%Y\') as paid_at'),
                DB::raw('sum(case when type = \'Receita\' then amount else amount * -1 end) as amount_per_period')
            ])
            ->where('paid_at', '>=', $start->format('Y-m-d'))
            ->where('paid_at', '<=', $end->format('Y-m-d'))
            ->orderBy('paid_at')
            ->groupBy(DB::raw('date_format(paid_at, \'%d/%m/%Y\')'))
            ->get()
            ->mapWithKeys(function (CarpentryFinanceMovement $carpentryFinanceMovement): array {
                return [
                    $carpentryFinanceMovement->paid_at => $carpentryFinanceMovement->amount_per_period
                ];
            })
            ->toArray();

        return [
            'chart' => [
                'type' => 'area',
                'height' => 300,
                'toolbar' => [
                    'show' => false,
                ]
            ],
            'series' => [
                [
                    'name' => 'Fluxo de caixa',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [

                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'fill' => [
                'type' => 'solid',
                'opacity' => 0.1,
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
            'grid' => [
                'show' => true,
                'position' => 'back',
            ],
            'markers' => [
                'size' => 5,
            ],
            'tooltip' => [
                'enabled' => true,
            ],
            'stroke' => [
                'width' => 2,
            ],
            'colors' => ['#047f3a'],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (value) {
                        if (!value) {
                            return '';
                        }

                        return value.toLocaleString('pt-BR',{ style: 'currency', currency: 'BRL' });
                    }
                }
            },
            dataLabels: {
                enabled: false
            }
        }
        JS);
    }

    protected function getFormSchema(): array
    {
        return [
            DatePicker::make('Pagamento em de')
                ->default(now()->setTimezone('-3:00')->startOfMonth()->format('Y-m-d')),
            DatePicker::make('Pagamento em até')
                ->default(now()->setTimezone('-3:00')->endOfMonth()->format('Y-m-d')),
        ];
    }
}
