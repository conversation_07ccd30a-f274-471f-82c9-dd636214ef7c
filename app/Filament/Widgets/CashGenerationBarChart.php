<?php

namespace App\Filament\Widgets;

use App\Models\CarpentryFinanceMovement;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class CashGenerationBarChart extends ApexChartWidget
{
    protected int | string | array $columnSpan = 12;
    protected static ?int $sort = 2;
    protected static ?string $chartId = 'cashGenerationChart';
    protected static ?string $heading = 'Geração de caixa';

    protected function getOptions(): array
    {
        $data = CarpentryFinanceMovement::query()
            ->select([
                DB::raw('sum(case when type = \'Receita\' then amount else 0 end) as Receitas'),
                DB::raw('sum(case when type = \'Despesa\' then amount else 0 end) as Despesas'),
                DB::raw('sum(case when type = \'Receita\' then amount else amount * -1 end) as Caixa'),
            ])
            ->whereMonth('paid_at', (int)$this->filterFormData['Mês'])
            ->whereYear('paid_at', (int)$this->filterFormData['Ano'])
            ->get()
            ->toArray()[0];

        // Calculate if Caixa is positive or negative
        $cashValue = $data['Caixa'] ?? 0;
        $cashColor = $cashValue >= 0 ? '#047f3a' : '#df6d69';

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'toolbar' => [
                    'show' => false,
                ]
            ],
            'series' => [
                [
                    'name' => 'Geração de caixa',
                    'data' => array_values($data),
                ],
            ],
            'xaxis' => [
                'categories' => array_keys($data),
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'yaxis' => [
                'forceNiceScale' => true,
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'colors' => ['#047f3a', '#df6d69', $cashColor],
            'plotOptions' => [
                'bar' => [
                    'distributed' => true,
                    'columnWidth' => '50%',
                ],
            ],
            'grid' => [
                'show' => true,
                'position' => 'back',
            ],
            'markers' => [
                'size' => 5,
            ],
            'tooltip' => [
                'enabled' => true,
            ],
            'stroke' => [
                'width' => 2,
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: {
                labels: {
                    formatter: function (value) {
                        if (!value) {
                            return '';
                        }

                        return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
                    }
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function (val, opt) {
                    if (!val) {
                        return '';
                    }

                    return opt.w.globals.labels[opt.dataPointIndex] + ': ' + val.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })
                },
                dropShadow: {
                    enabled: true
                }
            },
            legend: {
                show: false
            },
            states: {
                normal: {
                    filter: {
                        type: 'none'
                    }
                },
                hover: {
                    filter: {
                        type: 'none'
                    }
                },
                active: {
                    filter: {
                        type: 'none'
                    }
                }
            }
        }
        JS);
    }

    protected function getFormSchema(): array
    {
        return [
            Select::make('Mês')
                ->options([
                    '01' => 'Janeiro',
                    '02' => 'Fevereiro',
                    '03' => 'Março',
                    '04' => 'Abril',
                    '05' => 'Maio',
                    '06' => 'Junho',
                    '07' => 'Julho',
                    '08' => 'Agosto',
                    '09' => 'Setembro',
                    '10' => 'Outubro',
                    '11' => 'Novembro',
                    '12' => 'Dezembro',
                ])
                ->default(date('m'))
                ->placeholder('Selecione o mês'),
            Select::make('Ano')
                ->options(array_combine(
                    range(date('Y') - 5, date('Y') + 5),
                    range(date('Y') - 5, date('Y') + 5)
                ))
                ->default(date('Y'))
                ->placeholder('Selecione o ano'),
        ];
    }
}
