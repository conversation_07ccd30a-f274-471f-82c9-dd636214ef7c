<?php

namespace App\Filament\Widgets;

use App\Models\CarpentryFinanceMovement;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Support\Contracts\TranslatableContentDriver;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Widgets\Widget;
use Illuminate\Database\Eloquent\Builder;

class QuoteIncomesExpensesWidget extends Widget implements HasTable, HasForms
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected static string $view = 'filament.widgets.quote-incomes-expenses-widget';
    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    public ?string $quoteNumber = null;
    public ?string $month = null;
    public ?string $year = null;

    public $incomes = [];
    public $expenses = [];

    public function makeFilamentTranslatableContentDriver(): TranslatableContentDriver
    {
        return app(TranslatableContentDriver::class);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(CarpentryFinanceMovement::query())
            ->columns([
                TextColumn::make('customer_name')
                    ->label('Cliente'),
                TextColumn::make('quote_number')
                    ->label('Orçamento'),
                TextColumn::make('description')
                    ->label('Descrição'),
                TextColumn::make('paid_at')
                    ->label('Data')
                    ->date('d/m/Y'),
                TextColumn::make('amount')
                    ->label('Valor')
                    ->money('BRL'),
            ]);
    }

    protected function getFormSchema(): array
    {
        return [
            Grid::make(4)->schema([
                TextInput::make('quoteNumber')
                    ->label('Orçamento')
                    ->placeholder('Número do orçamento')
                    ->columnSpan(2),
                Select::make('month')
                    ->label('Mês')
                    ->columnStart(3)
                    ->options([
                        '01' => 'Janeiro',
                        '02' => 'Fevereiro',
                        '03' => 'Março',
                        '04' => 'Abril',
                        '05' => 'Maio',
                        '06' => 'Junho',
                        '07' => 'Julho',
                        '08' => 'Agosto',
                        '09' => 'Setembro',
                        '10' => 'Outubro',
                        '11' => 'Novembro',
                        '12' => 'Dezembro',
                    ])
                    ->placeholder('Selecione o mês'),
                Select::make('year')
                    ->label('Ano')
                    ->columnStart(4)
                    ->options(array_combine(
                        range(date('Y') - 5, date('Y') + 5),
                        range(date('Y') - 5, date('Y') + 5)
                    ))
                    ->default(date('Y'))
                    ->placeholder('Selecione o ano'),
            ]),
        ];
    }

    public function mount(): void
    {
        $this->fetchData();
    }

    public function updatedQuoteNumber(): void
    {
        $this->fetchData();
    }

    public function updatedMonth(): void
    {
        $this->fetchData();
    }

    public function updatedYear(): void
    {
        $this->fetchData();
    }

    public function fetchData(): void
    {
        $query = CarpentryFinanceMovement::query();

        if ($this->quoteNumber) {
            $query->where('quote_number', $this->quoteNumber);
        } else {
            $query->whereRaw('0 = 1');
        }

        if ($this->month && $this->year) {
            $startDate = Carbon::createFromDate($this->year, $this->month, 1)->startOfMonth();
            $endDate = Carbon::createFromDate($this->year, $this->month, 1)->endOfMonth();

            $query->whereBetween('paid_at', [$startDate, $endDate]);
        } elseif ($this->year) {
            $startDate = Carbon::createFromDate($this->year, 1, 1)->startOfYear();
            $endDate = Carbon::createFromDate($this->year, 12, 31)->endOfYear();

            $query->whereBetween('paid_at', [$startDate, $endDate]);
        }

        $this->incomes = $query->clone()->where('type', 'Receita')->get();
        $this->expenses = $query->clone()->where('type', 'Despesa')->get();
    }
}
