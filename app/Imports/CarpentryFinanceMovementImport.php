<?php

namespace App\Imports;

use App\Models\CarpentryFinanceMovement;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Throwable;

class CarpentryFinanceMovementImport implements ToCollection, WithStartRow
{
    public function startRow(): int
    {
        return 7;
    }

    public function collection(Collection $collection)
    {
        $collection->each(function (Collection $row): void {
            $itemRow = $row->toArray();

            if (is_null($itemRow[9])) {
                return;
            }

            if (!is_numeric($itemRow[9])) {
                return;
            }

            try {
                $referringDate = Carbon::createFromFormat('d/m/Y', $itemRow[5]);
            } catch (Throwable) {
                $referringDate = $itemRow[5];
            }

            try {
                $expiresAt = Carbon::createFromFormat('d/m/Y', $itemRow[6]);
            } catch (Throwable) {
                $expiresAt = $itemRow[6];
            }

            try {
                $paidAt = Carbon::createFromFormat('d/m/Y', $itemRow[7]);
            } catch (Throwable) {
                $paidAt = $itemRow[7];
            }

            CarpentryFinanceMovement::updateOrCreate([
                'type' => $itemRow[0],
                'chart_of_accounts' => $itemRow[1],
                'customer_name' => $itemRow[2],
                'description' => $itemRow[3],
                'quote_number' => $itemRow[4],
                'referring_date' => $referringDate,
                'expires_at' => $expiresAt,
                'paid_at' => $paidAt,
                'payment_type' => $itemRow[8],
                'amount' => $itemRow[9],
            ]);
        });
    }
}
