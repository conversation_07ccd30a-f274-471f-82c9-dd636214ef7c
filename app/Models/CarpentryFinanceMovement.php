<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * CarpentryFinanceMovement model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $type
 * @property  string $chart_of_accounts
 * @property  string $customer_name
 * @property  string $description
 * @property  string $quote_number
 * @property  \Carbon\Carbon $referring_date
 * @property  \Carbon\Carbon $expires_at
 * @property  \Carbon\Carbon $paid_at
 * @property  string $payment_type
 * @property  float $amount
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 */
class CarpentryFinanceMovement extends Model
{
    protected $fillable = [
        'type',
        'chart_of_accounts',
        'customer_name',
        'description',
        'quote_number',
        'referring_date',
        'expires_at',
        'paid_at',
        'payment_type',
        'amount',
    ];
}
